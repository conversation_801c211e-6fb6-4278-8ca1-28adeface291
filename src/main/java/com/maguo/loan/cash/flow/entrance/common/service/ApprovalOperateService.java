package com.maguo.loan.cash.flow.entrance.common.service;

import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;

import com.jinghang.common.http.HttpFramework;
import com.jinghang.common.util.HttpUtil;
import com.jinghang.common.util.JsonUtil;
import com.jinghang.common.util.StringUtil;
import com.maguo.loan.cash.flow.entity.ApprovalRecord;
import com.maguo.loan.cash.flow.entity.Order;
import com.maguo.loan.cash.flow.entity.PreOrder;
import com.maguo.loan.cash.flow.entity.UserContactInfo;
import com.maguo.loan.cash.flow.entity.UserDevice;
import com.maguo.loan.cash.flow.entity.UserFace;
import com.maguo.loan.cash.flow.entity.UserInfo;
import com.maguo.loan.cash.flow.entity.UserOcr;
import com.maguo.loan.cash.flow.entity.UserRegister;
import com.maguo.loan.cash.flow.entity.UserRiskRecord;
import com.maguo.loan.cash.flow.entrance.common.constant.CommonBaseConstant;
import com.maguo.loan.cash.flow.entrance.common.covert.CommonApiBaseConstant;
import com.maguo.loan.cash.flow.entrance.common.covert.CommonApiCovert;
import com.maguo.loan.cash.flow.entrance.common.covert.CommonApiEnumCovert;
import com.maguo.loan.cash.flow.entrance.common.dto.request.approval.ApprovalReqDTO;
import com.maguo.loan.cash.flow.entrance.common.dto.request.approval.QueryApprovalReqDTO;
import com.maguo.loan.cash.flow.entrance.common.dto.response.approval.ApprovalResDTO;
import com.maguo.loan.cash.flow.entrance.common.enums.ApprovalStatus;
import com.maguo.loan.cash.flow.entrance.common.enums.ImageFormat;
import com.maguo.loan.cash.flow.entrance.common.exception.CommonApiBizException;
import com.maguo.loan.cash.flow.entrance.common.exception.CommonApiResultCode;
import com.maguo.loan.cash.flow.enums.OrderState;
import com.maguo.loan.cash.flow.enums.PreOrderState;
import com.maguo.loan.cash.flow.enums.RightsLevel;
import com.maguo.loan.cash.flow.enums.WhetherState;
import com.maguo.loan.cash.flow.repository.ApprovalRecordRepository;
import com.maguo.loan.cash.flow.repository.PreOrderRepository;
import com.maguo.loan.cash.flow.repository.UserRegisterRepository;
import com.maguo.loan.cash.flow.repository.UserRiskRecordRepository;
import com.maguo.loan.cash.flow.service.CheckService;
import com.maguo.loan.cash.flow.service.FileService;
import com.maguo.loan.cash.flow.service.LockService;
import com.maguo.loan.cash.flow.service.Locker;
import com.maguo.loan.cash.flow.service.MqService;
import com.maguo.loan.cash.flow.service.OrderService;
import com.maguo.loan.cash.flow.service.UserFileService;
import com.maguo.loan.cash.flow.service.UserService;
import com.maguo.loan.cash.flow.service.agreement.AgreementService;
import com.maguo.loan.cash.flow.util.Base64Utils;
import com.maguo.loan.cash.flow.util.DateUtil;
import com.maguo.loan.cash.flow.util.ImageUtil;
import com.maguo.loan.cash.flow.util.RedisKeyConstants;
import com.maguo.loan.cash.flow.util.ThreadLocalUtil;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.time.Duration;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.UUID;
import java.util.stream.Collectors;

import static com.maguo.loan.cash.flow.job.state.OrderStateJob.DATE_SUBTRACT_THREE;
import static com.maguo.loan.cash.flow.job.susupend.SuspendActivationJob.LOCK_RELEASE_SECOND;


/**
 * 进件之后相关逻辑
 *
 * <AUTHOR>
 * @date 2024/8/20
 */
@Service
public class ApprovalOperateService {

    private static final Logger logger = LoggerFactory.getLogger(ApprovalOperateService.class);

    @Value("${oss.bucket.name}")
    private String ossBucket;

    @Autowired
    private PreOrderRepository preOrderRepository;

    @Autowired
    private CheckService checkService;

    @Autowired
    private ApprovalRecordRepository approvalRecordRepository;

    @Autowired
    private FileService fileService;

    @Autowired
    private UserService userService;

    @Autowired
    private UserRegisterRepository userRegisterRepository;

    @Autowired
    private UserFileService userFileService;

    @Autowired
    private AgreementService agreementService;

    @Autowired
    private MqService mqService;

    @Autowired
    private UserRiskRecordRepository userRiskRecordRepository;

    @Autowired
    private OrderService orderService;

    @Autowired
    private LockService lockService;

    /**
     * 进件加锁
     */

    public ApprovalResDTO approvalPre(ApprovalReqDTO approvalReqDTO) {
        String lockKey = RedisKeyConstants.APPROVAL_APPLY + approvalReqDTO.getPhotos().getCertNo();
        Locker lock = lockService.getLock(lockKey);
        try {
            boolean locked = lock.tryLock(Duration.ZERO, Duration.ofSeconds(LOCK_RELEASE_SECOND));
            if (!locked) {
                logger.error("优品标准api,进件申请,不可重复提交:{}", approvalReqDTO.getPhotos().getCertNo());
                throw new CommonApiBizException(CommonApiResultCode.REPEAT_SUBMIT);
            }
            return approval(approvalReqDTO);
        } catch (CommonApiBizException ce) {
            throw ce;
        } catch (InterruptedException e) {
            logger.error("优品标准api,进件申请,进件异常", e);
            throw new CommonApiBizException(CommonApiResultCode.SYSTEM_ERROR);
        } finally {
            lock.unlock();
        }
    }

    /**
     * 进件
     */
    private ApprovalResDTO approval(ApprovalReqDTO reqDTO) {

        String certValidStart = reqDTO.getPhotos().getCertValidStart();
        String certValidEnd = reqDTO.getPhotos().getCertValidEnd();
        if (!"长期".equals(certValidEnd) && LocalDate.parse(certValidStart).isAfter(LocalDate.parse(certValidEnd))) {
            throw new CommonApiBizException(CommonApiResultCode.PARAM_CERTVAILD_FAIL);
        }
        if (Objects.nonNull(reqDTO.getDeviceInfo()) && JsonUtil.toJsonString(reqDTO.getDeviceInfo()).length() > CommonApiBaseConstant.JSON_MAX_LENGTH) {
            throw new CommonApiBizException(CommonApiResultCode.DEVICEINFO_FAIL);
        }
        if (JsonUtil.toJsonString(reqDTO.getRelations()).length() > CommonApiBaseConstant.JSON_MAX_LENGTH) {
            throw new CommonApiBizException(CommonApiResultCode.RELATION_FAIL);
        }
        ApprovalResDTO result = new ApprovalResDTO();
        result.setPartnerOrderNo(reqDTO.getPartnerOrderNo());
        result.setAmount(reqDTO.getApplyAmount());
        result.setPeriod(reqDTO.getApplyPeriod());
        result.setPartnerOrderNo(reqDTO.getPartnerOrderNo());

        // 根据合作方单号，判断是否已存在预订单
        String partnerOrderNo = reqDTO.getPartnerOrderNo();
        Optional<PreOrder> preOrderOptional = preOrderRepository.findByOrderNoAndFlowChannel(partnerOrderNo, ThreadLocalUtil.getFlowChannel());
        if (preOrderOptional.isPresent()) {
            // 已存在预订单
            logger.info("已存在该订单,partnerOrderNo:{}", partnerOrderNo);
            result.setApprovalStatus(ApprovalStatus.AUDITING.name());
            return result;
        }

        // 保存预订单
        PreOrder preOrder = CommonApiCovert.INSTANCE.toPreOrder(reqDTO);
        preOrder.setFlowChannel(ThreadLocalUtil.getFlowChannel());
        preOrder = preOrderRepository.save(preOrder);

        // 保存进件记录

        ApprovalRecord approvalRecord =
            approvalRecordRepository.findByPartnerOrderNoAndFlowChannel(reqDTO.getPartnerOrderNo(), ThreadLocalUtil.getFlowChannel()).orElse(null);
        ApprovalRecord record = CommonApiCovert.INSTANCE.toApprovalRecord(reqDTO);
        if (Objects.nonNull(approvalRecord)) {
            record.setId(approvalRecord.getId());
            record.setRevision(approvalRecord.getRevision());
        }
        record.setFlowChannel(ThreadLocalUtil.getFlowChannel());
        record.setApplyTime(LocalDateTime.now());


        // 解析参数中base64图片,上传到oss
        record.setFaceBucket(ossBucket);
        record.setHeadBucket(ossBucket);
        record.setNationalEmblemBucket(ossBucket);

        // 身份证国徽面照
        String nationalEmblem = reqDTO.getPhotos().getNationalEmblemImage();

        // 身份证人头面
        String headImage = reqDTO.getPhotos().getHeadImage();

        // 活体照片
        String faceImage = reqDTO.getPhotos().getFaceImage();

        ImageFormat imageFormat = CommonApiEnumCovert.INSTANCE.toImageFormat(reqDTO.getImageFormat());
        if (Objects.isNull(imageFormat)) {
            throw new CommonApiBizException(CommonApiResultCode.PARAM_IMAGE_FORMAT_FAIL);
        }
        if (imageFormat == ImageFormat.LINK) {
            //链接方式上传
            preOrder = uploadFileByUrl(headImage, nationalEmblem, faceImage, record, preOrder);
        } else {
            // base64方式
            preOrder = uploadFileByBase64(headImage, nationalEmblem, faceImage, record, preOrder);
        }

        record = approvalRecordRepository.save(record);

        // 图片下载失败
        if (preOrder.getPreOrderState() == PreOrderState.AUDIT_REJECT) {
            result.setApprovalStatus(ApprovalStatus.AUDIT_REJECT.name());
            result.setRejectReason(preOrder.getRemark());
            return result;
        }

        //判断是否存在在途订单
        String onOrder = checkService.onOrderOrRiskByCertNo(preOrder);
        if (StringUtil.isNotBlank(onOrder)) {
            //进件失败
            preOrder.setPreOrderState(PreOrderState.AUDIT_REJECT);
            preOrder.setIsReject(WhetherState.Y);
            preOrder.setRemark(onOrder);
            preOrderRepository.save(preOrder);
            result.setApprovalStatus(ApprovalStatus.AUDIT_REJECT.name());
            result.setRejectReason(onOrder);
            return result;
        }
        //注册用户
        UserRegister userRegister = userService.findUserRegisterByOpenId(preOrder.getOpenId(), preOrder.getFlowChannel());
        if (Objects.isNull(userRegister)) {
            userRegister = userService.registerRecord(preOrder.getMobile(), preOrder.getOpenId(), preOrder.getFlowChannel());
        }
        UserInfo userInfo = createUserInfo(record);
        UserOcr userOcr = createUserOcr(record);
        UserFace userFace = createUserFace(record);
        UserDevice userDevice = createUserDevice(record);
        List<UserContactInfo> contactInfos = createUserContactInfos(record);
        UserRiskRecord riskRecord = userService.register(userInfo, userOcr, userFace, userDevice, contactInfos, ThreadLocalUtil.getFlowChannel(),preOrder);
        //保存风控id
        preOrder.setRiskId(riskRecord.getId());
        preOrder = preOrderRepository.save(preOrder);

        String userId = riskRecord.getUserId();
        //填充userId
        userRegister.setUserId(userId);
        userRegisterRepository.save(userRegister);

        userFileService.saveIdCardFace(userId, userOcr.getHeadOssBucket(), userOcr.getHeadOssKey());
        userFileService.saveIdCardNation(userId, userOcr.getNationOssBucket(), userOcr.getNationOssKey());
        userFileService.saveFaceOcr(userId, userFace.getOssBucket(), userFace.getOssKey());

        // 签章 和 内部风控
      //  agreementService.applyRegisterSign(riskRecord.getId());
        mqService.submitRiskApply(riskRecord.getId());

        //更新预订单为审核中
        preOrder.setPreOrderState(PreOrderState.AUDITING);
        preOrderRepository.save(preOrder);

        result.setApprovalStatus(ApprovalStatus.AUDITING.name());
        return result;
    }

    /**
     * 查询进件结果
     */
    public ApprovalResDTO queryApproval(QueryApprovalReqDTO reqDTO) {
        ApprovalResDTO result = new ApprovalResDTO();

        Optional<ApprovalRecord> approvalRecord =
            approvalRecordRepository.findByPartnerOrderNoAndFlowChannel(reqDTO.getPartnerOrderNo(), ThreadLocalUtil.getFlowChannel());
        if (Objects.isNull(approvalRecord)) {
            throw new CommonApiBizException(CommonApiResultCode.APPROVAL_NOT_EXIST);
        }

        result.setPartnerOrderNo(reqDTO.getPartnerOrderNo());
        Optional<PreOrder> preOrderOptional = preOrderRepository.findByOrderNoAndFlowChannel(reqDTO.getPartnerOrderNo(), ThreadLocalUtil.getFlowChannel());
        if (preOrderOptional.isEmpty()) {
            logger.error("优品标准api,进件查询:预订单不存在,PartnerOrderNo:{}", reqDTO.getPartnerOrderNo());
            result.setApprovalStatus(ApprovalStatus.AUDIT_REJECT.name());
            result.setRejectReason("订单不存在");
            return result;
        }
        PreOrder preOrder = preOrderOptional.get();
        switch (preOrder.getPreOrderState()) {
            case INIT, AUDITING -> {
                result.setApprovalStatus(ApprovalStatus.AUDITING.name());
            }
            case AUDIT_PASS -> {
                result.setApprovalStatus(ApprovalStatus.AUDIT_PASS.name());
                Order order = orderService.findByRiskId(preOrder.getRiskId());
                if (Objects.isNull(order)) {
                    throw new CommonApiBizException(CommonApiResultCode.ORDER_NOT_EXIST);
                }
                result.setRightsSwitch(order.getApproveRights() != RightsLevel.NONE);
                result.setPeriod(order.getApplyPeriods());
                result.setOrderId(order.getId());
                result.setAmount(order.getApproveAmount());
                result.setApprovalRate(order.getIrrRate());
                if (order.getOrderState() == OrderState.LOAN_CANCEL && order.getOrderSubmitState() == WhetherState.N) {
                    // 额度失效
                    result.setApprovalStatus(ApprovalStatus.QUOTA_INVALID.name());
                } else {
                    LocalDateTime applyTime = order.getApplyTime();
                    LocalDateTime time = applyTime.plusDays(DATE_SUBTRACT_THREE);
                    result.setExpireTime(time);
                }
            }
            case AUDIT_REJECT -> {
                result.setApprovalStatus(ApprovalStatus.AUDIT_REJECT.name());
                LocalDateTime time = preOrder.getCreatedTime().plusDays(CommonApiBaseConstant.DAYS_INTERVAL);
                result.setFreeTime(time);
                result.setRejectReason(ApprovalStatus.AUDIT_REJECT.getRejectReason());
            }
            default -> {
            }
        }

        return result;
    }

    private List<UserContactInfo> createUserContactInfos(ApprovalRecord approvalRecord) {
        List<ApprovalReqDTO.Relation> relations = JSONArray.parseArray(approvalRecord.getRelations(), ApprovalReqDTO.Relation.class);
        Set<String> set = relations.stream().map(ApprovalReqDTO.Relation::getPhone).collect(Collectors.toSet());
        if (set.size() < relations.size()) {
            throw new CommonApiBizException(CommonApiResultCode.RELATION_PHONE_FAIL);
        }
        return CommonApiCovert.INSTANCE.toUserContactInfos(relations);
    }

    private UserDevice createUserDevice(ApprovalRecord approvalRecord) {
        String deviceInfo = approvalRecord.getDeviceInfo();
        JSONObject jsonObject = JSONObject.parseObject(deviceInfo);
        UserDevice userDevice = new UserDevice();
        if (StringUtil.isNotBlank(approvalRecord.getGps())) {
            userDevice.setGps(approvalRecord.getGps());
        }
        if (Objects.isNull(jsonObject)) {
            return userDevice;
        }
        String deviceType = jsonObject.getString("deviceType");
        userDevice.setOsType(deviceType);
        userDevice.setOsVersion(jsonObject.getString("osVersion"));
        userDevice.setIp(jsonObject.getString("ip"));
        userDevice.setIdFa(jsonObject.getString("idfa"));
        userDevice.setOaId(jsonObject.getString("oaId"));
        userDevice.setUa(jsonObject.getString("ua"));
        userDevice.setIsSimulator(jsonObject.getString("isSimulator"));
        userDevice.setModel(jsonObject.getString("model"));
        return userDevice;
    }

    private UserFace createUserFace(ApprovalRecord applyRecord) {
        UserFace userFace = new UserFace();
        userFace.setFaceChannel(applyRecord.getFaceSource());
        userFace.setFaceTime(applyRecord.getFaceCollectTime());
        userFace.setFaceScore(applyRecord.getFaceScore());
        userFace.setOssBucket(ossBucket);
        userFace.setOssKey(applyRecord.getFaceOssKey());
        return userFace;
    }

    private UserOcr createUserOcr(ApprovalRecord applyRecord) {
        UserOcr userOcr = new UserOcr();
        userOcr.setCertNo(applyRecord.getCertNo());
        userOcr.setCertAddress(applyRecord.getCertAddress());
        userOcr.setCertSignOrg(applyRecord.getCertSignOrg());
        userOcr.setCertValidStart(applyRecord.getCertValidStart());
        userOcr.setCertValidEnd(applyRecord.getCertValidEnd());
        userOcr.setHeadOssBucket(ossBucket);
        userOcr.setNationOssBucket(ossBucket);
        userOcr.setHeadOssKey(applyRecord.getHeadOssKey());
        userOcr.setNationOssKey(applyRecord.getNationalEmblemOssKey());
        String certNo = applyRecord.getCertNo();
        userOcr.setCityCode(CommonApiBaseConstant.toCityCode(certNo));
        userOcr.setProvinceCode(CommonApiBaseConstant.toProvinceCode(certNo));
        userOcr.setDistrictCode(CommonApiBaseConstant.toDistrictCode(certNo));
        userOcr.setGender(applyRecord.getGender());
        userOcr.setNation(applyRecord.getNation());
        return userOcr;
    }

    private UserInfo createUserInfo(ApprovalRecord applyRecord) {
        return CommonApiCovert.INSTANCE.toUserInfo(applyRecord);
    }

    /**
     * 通过base64上传文件
     */
    private PreOrder uploadFileByBase64(String headImage, String nationalEmblem, String faceImage, ApprovalRecord record, PreOrder preOrder) {
        String mobile = record.getMobile();
        try {
            record.setHeadImage(null);
            record.setFaceImage(null);
            record.setNationalEmblemImage(null);
            // 人像面
            byte[] headImageArray = Base64Utils.decodeBase64(headImage);
            String headImageOssKey =
                uploadToOss(new ByteArrayInputStream(headImageArray), CommonApiBaseConstant.HEAD, mobile, ImageUtil.detectMimeExt(headImageArray));
            record.setHeadOssKey(headImageOssKey);

            // 国徽面
            byte[] nationalEmblemArray = Base64Utils.decodeBase64(nationalEmblem);
            String nationalEmblemOssKey =
                uploadToOss(new ByteArrayInputStream(nationalEmblemArray), CommonApiBaseConstant.NATIONAL_EMBLEM, mobile,
                    ImageUtil.detectMimeExt(nationalEmblemArray));
            record.setNationalEmblemOssKey(nationalEmblemOssKey);

            // 人脸
            byte[] faceImageArray = Base64Utils.decodeBase64(faceImage);
            String faceImageUrlOssKey =
                uploadToOss(new ByteArrayInputStream(faceImageArray), CommonApiBaseConstant.FACE, mobile, ImageUtil.detectMimeExt(faceImageArray));
            record.setFaceOssKey(faceImageUrlOssKey);

            return preOrder;
        } catch (IOException e) {
            preOrder.setPreOrderState(PreOrderState.AUDIT_REJECT);
            preOrder.setIsReject(WhetherState.Y);
            preOrder.setRemark("影像文件下载异常");
            preOrder = preOrderRepository.save(preOrder);
            logger.error("优品标准api,影像文件下载异常,{}", preOrder.getOrderNo(), e);
            return preOrder;
        }
    }

    /**
     * 通过链接上传文件
     */
    private PreOrder uploadFileByUrl(String headImageUrl, String nationalEmblemUrl, String faceImageUrl, ApprovalRecord record, PreOrder preOrder) {
        String mobile = record.getMobile();

        InputStream headImage;
        InputStream nationImage;
        InputStream faceImage;
        if (headImageUrl.length() > CommonBaseConstant.URL_MAX) {
            throw new CommonApiBizException("身份证人头面链接长度过长[1000]", CommonApiResultCode.PARAM_FAIL);
        } else if (nationalEmblemUrl.length() > CommonBaseConstant.URL_MAX) {
            throw new CommonApiBizException("身份证国徽面照链接长度过长[1000]", CommonApiResultCode.PARAM_FAIL);
        } else if (faceImageUrl.length() > CommonBaseConstant.URL_MAX) {
            throw new CommonApiBizException("活体照片链接长度过长[1000]", CommonApiResultCode.PARAM_FAIL);
        }
        try {
            // 人像面
            headImage = HttpUtil.getWithStream(HttpFramework.HTTPCLIENT5, headImageUrl);
            String headImageOssKey =
                uploadToOss(headImage, CommonApiBaseConstant.HEAD, mobile, ImageUtil.detectMimeExt(headImage.readAllBytes()));
            record.setHeadOssKey(headImageOssKey);

            // 国徽面
            nationImage = HttpUtil.getWithStream(HttpFramework.HTTPCLIENT5, nationalEmblemUrl);
            String nationalEmblemOssKey =
                uploadToOss(nationImage, CommonApiBaseConstant.NATIONAL_EMBLEM, mobile, ImageUtil.detectMimeExt(nationImage.readAllBytes()));
            record.setNationalEmblemOssKey(nationalEmblemOssKey);

            // 人脸
            faceImage = HttpUtil.getWithStream(HttpFramework.HTTPCLIENT5, faceImageUrl);
            String faceImageUrlOssKey =
                uploadToOss(faceImage, CommonApiBaseConstant.FACE, mobile, ImageUtil.detectMimeExt(faceImage.readAllBytes()));
            record.setFaceOssKey(faceImageUrlOssKey);

            return preOrder;
        } catch (Exception e) {
            preOrder.setPreOrderState(PreOrderState.AUDIT_REJECT);
            preOrder.setIsReject(WhetherState.Y);
            preOrder.setRemark("影像文件下载异常");
            preOrder = preOrderRepository.save(preOrder);
            logger.error("优品标准api,影像文件下载异常,{}", preOrder.getOrderNo(), e);
            return preOrder;
        }
    }

    /**
     * 上传oss
     */
    private String uploadToOss(InputStream inputStream, String picType, String openId, String imageType) throws IOException {
        inputStream.reset();
        String ossKey = generateOssPicKey(openId, picType, imageType);

        try {
            fileService.uploadOss(ossBucket, ossKey, inputStream);
            return ossKey;
        } catch (Exception e) {
            logger.error("优品标准api uploadToOss error, openId: {}, fileType: {}", openId, picType);
            throw new CommonApiBizException(CommonApiResultCode.UPLOAD_FAIL);
        }
    }

    /**
     * 通过链接后缀获取后缀名
     */
    private String getImageType(String imageUrl) {
        int lastIndexOf = imageUrl.lastIndexOf(".");
        if (lastIndexOf != -1 && lastIndexOf < imageUrl.length() - 1) {
            return imageUrl.substring(lastIndexOf + 1);
        } else {
            throw new CommonApiBizException(CommonApiResultCode.PARAM_LINK_FAIL);
        }
    }

    private String generateOssPicKey(String openId, String prefix, String imageType) {
        String dayStr = DateUtil.formatLocalDateTime(LocalDateTime.now(), DateTimeFormatter.BASIC_ISO_DATE);
        return "commonApi/info/" + dayStr + "/" + openId + "/" + prefix + "_" + UUID.randomUUID().toString().replaceAll("-", "") + "." + imageType;
    }

}
