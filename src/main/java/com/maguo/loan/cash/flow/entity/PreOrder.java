package com.maguo.loan.cash.flow.entity;


import com.jinghang.capital.api.dto.BankChannel;
import com.maguo.loan.cash.flow.enums.AmountType;
import com.maguo.loan.cash.flow.enums.ApplicationSource;
import com.maguo.loan.cash.flow.enums.EquityRecipient;
import com.maguo.loan.cash.flow.enums.FlowChannel;
import com.maguo.loan.cash.flow.enums.IsIncludingEquity;
import com.maguo.loan.cash.flow.enums.PreOrderState;
import com.maguo.loan.cash.flow.enums.WhetherState;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.Table;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 */
@Entity
@Table(name = "pre_order")
public class PreOrder extends BaseEntity {

    /**
     * 订单编号
     */
    private String orderNo;

    /**
     * 流量渠道
     */
    @Enumerated(EnumType.STRING)
    private FlowChannel flowChannel;

    @Enumerated(EnumType.STRING)
    private AmountType amountType;

    /**
     * 用户Id
     */
    private String openId;

    /**
     * 姓名
     */
    private String name;

    /**
     * 用户手机号码
     */
    private String mobile;

    /**
     * 身份证号
     */
    private String certNo;

    /**
     * 订单创建时间
     */
    private LocalDateTime applyTime;

    /**
     * 申请金额
     */
    private BigDecimal applyAmount;

    /**
     * 申请期限
     */
    private Integer applyPeriods;

    /**
     * 预订单状态
     */
    @Enumerated(EnumType.STRING)
    private PreOrderState preOrderState;

    /**
     * 是否已被拒
     */
    @Enumerated(EnumType.STRING)
    private WhetherState isReject;

    /**
     * 风控id
     */
    private String riskId;

    @Enumerated(EnumType.STRING)
    private ApplicationSource applicationSource;

    /**
     * 是否含权益
     * Y:含权益：N:不含权益
     */
    @Enumerated(EnumType.STRING)
    private IsIncludingEquity isIncludingEquity;

    /**
     * 权益收取方
     * O：外部,I：内部（默认O：外部）
     */
    @Enumerated(EnumType.STRING)
    private EquityRecipient equityRecipient;


    @Enumerated(EnumType.STRING)
    private WhetherState isAssignBankChannel;

    @Enumerated(EnumType.STRING)
    private BankChannel bankChannel;

    public IsIncludingEquity getIsIncludingEquity() {
        return isIncludingEquity;
    }

    public void setIsIncludingEquity(IsIncludingEquity isIncludingEquity) {
        this.isIncludingEquity = isIncludingEquity;
    }

    public EquityRecipient getEquityRecipient() {
        return equityRecipient;
    }

    public void setEquityRecipient(EquityRecipient equityRecipient) {
        this.equityRecipient = equityRecipient;
    }

    /*
     * 流量客群
     */
    private String sourceCode;
    /**
     * 渠道标识
     */
    private String applyChannel;

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public FlowChannel getFlowChannel() {
        return flowChannel;
    }

    public void setFlowChannel(FlowChannel flowChannel) {
        this.flowChannel = flowChannel;
    }

    public AmountType getAmountType() {
        return amountType;
    }

    public void setAmountType(AmountType amountType) {
        this.amountType = amountType;
    }

    public String getOpenId() {
        return openId;
    }

    public void setOpenId(String openId) {
        this.openId = openId;
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public LocalDateTime getApplyTime() {
        return applyTime;
    }

    public void setApplyTime(LocalDateTime applyTime) {
        this.applyTime = applyTime;
    }

    public BigDecimal getApplyAmount() {
        return applyAmount;
    }

    public void setApplyAmount(BigDecimal applyAmount) {
        this.applyAmount = applyAmount;
    }

    public Integer getApplyPeriods() {
        return applyPeriods;
    }

    public void setApplyPeriods(Integer applyPeriods) {
        this.applyPeriods = applyPeriods;
    }

    public String getRiskId() {
        return riskId;
    }

    public void setRiskId(String riskId) {
        this.riskId = riskId;
    }

    @Override
    protected String prefix() {
        return "POR";
    }


    public WhetherState getIsReject() {
        return isReject;
    }

    public void setIsReject(WhetherState isReject) {
        this.isReject = isReject;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getCertNo() {
        return certNo;
    }

    public void setCertNo(String certNo) {
        this.certNo = certNo;
    }

    public PreOrderState getPreOrderState() {
        return preOrderState;
    }

    public void setPreOrderState(PreOrderState preOrderState) {
        this.preOrderState = preOrderState;
    }

    public ApplicationSource getApplicationSource() {
        return applicationSource;
    }

    public void setApplicationSource(ApplicationSource applicationSource) {
        this.applicationSource = applicationSource;
    }

    public WhetherState getIsAssignBankChannel() {
        return isAssignBankChannel;
    }

    public void setIsAssignBankChannel(WhetherState isAssignBankChannel) {
        this.isAssignBankChannel = isAssignBankChannel;
    }

    public BankChannel getBankChannel() {
        return bankChannel;
    }

    public void setBankChannel(BankChannel bankChannel) {
        this.bankChannel = bankChannel;
    }

    public String getSourceCode() {
        return sourceCode;
    }

    public void setSourceCode(String custSource) {
        this.sourceCode = custSource;
    }

    public String getApplyChannel() {
        return applyChannel;
    }

    public void setApplyChannel(String applyChannel) {
        this.applyChannel = applyChannel;
    }
}
