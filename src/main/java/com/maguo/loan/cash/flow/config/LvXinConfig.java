package com.maguo.loan.cash.flow.config;

import com.jinghang.common.util.DateUtil;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

import java.util.Date;

/**
 * 宝付配置
 */
@Configuration
public class LvXinConfig {
    /**
     * 绿信签章sftpPath
     */
    @Value("${lvxin.sftp.username}")
    private String sftpUserName;
    /**
     * 绿信签章sftpPath
     */
    @Value("${lvxin.sftp.password}")
    private String sftpPassword;
    /**
     * 绿信签章sftpPath
     */
    @Value("${lvxin.sftp.ip}")
    private String sftpIp;
    /**
     * 绿信签章sftpPath
     */
    @Value("${lvxin.sftp.port}")
    private Integer sftpPort;
    /**
     * 绿信签章sftpPath
     */
    @Value("${lvxin.agreement.sftpPath}")
    private String agreementSftpPath;

    //权益签章
    @Value("${lvxin.IncludingEquity.agreement.sftpPath}")
    private String includingEquityAgreementSftpPath;
    /**
     * 绿信结清证明sftpPath
     */
    @Value("${lvxin.clearVoucher.sftpPath}")
    private String clearVoucherSftpPath;
    @Value("${lvxin.loan.sftpPath}")
    private String loanSftpPath;

    public String getLoanSftpPath() {
        return loanSftpPath;
    }

    public void setLoanSftpPath( String loanSftpPath ) {
        this.loanSftpPath = loanSftpPath;
    }


    /**
     * 绿信权益结清证明sftpPath
     */
    @Value("${lvxin.IncludingEquity.clearVoucher.sftpPath}")
    private String IncludingEquityClearVoucherSftpPath;
    public String getSftpUserName() {
        return sftpUserName;
    }

    public void setSftpUserName(String sftpUserName) {
        this.sftpUserName = sftpUserName;
    }

    public String getSftpPassword() {
        return sftpPassword;
    }

    public void setSftpPassword(String sftpPassword) {
        this.sftpPassword = sftpPassword;
    }

    public String getSftpIp() {
        return sftpIp;
    }

    public void setSftpIp(String sftpIp) {
        this.sftpIp = sftpIp;
    }

    public Integer getSftpPort() {
        return sftpPort;
    }

    public void setSftpPort(Integer sftpPort) {
        this.sftpPort = sftpPort;
    }

    public String getAgreementSftpPath(String applyNo) {
        return agreementSftpPath + DateUtil.formatShort(new Date()) + "/" + applyNo;
    }

    public String getIncludingEquityAgreementSftpPath(String applyNo) {
        return includingEquityAgreementSftpPath + DateUtil.formatShort(new Date()) + "/" + applyNo;
    }

    public void setIncludingEquityAgreementSftpPath(String includingEquityAgreementSftpPath) {
        this.includingEquityAgreementSftpPath = includingEquityAgreementSftpPath;
    }

    public void setAgreementSftpPath(String agreementSftpPath) {
        this.agreementSftpPath = agreementSftpPath;
    }

    public String getClearVoucherSftpPath(String applyNo) {
        return clearVoucherSftpPath+ DateUtil.formatShort(new Date()) + "/" + applyNo;
    }

    public void setClearVoucherSftpPath(String clearVoucherSftpPath) {
        this.clearVoucherSftpPath = clearVoucherSftpPath;
    }

    public String getIncludingEquityClearVoucherSftpPath(String applyNo) {
        return IncludingEquityClearVoucherSftpPath+ DateUtil.formatShort(new Date()) + "/" + applyNo;
    }

    public void setIncludingEquityClearVoucherSftpPath(String includingEquityClearVoucherSftpPath) {
        IncludingEquityClearVoucherSftpPath = includingEquityClearVoucherSftpPath;
    }
}
