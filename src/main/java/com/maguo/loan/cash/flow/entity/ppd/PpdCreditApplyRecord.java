package com.maguo.loan.cash.flow.entity.ppd;

import com.maguo.loan.cash.flow.entity.BaseEntity;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @Description
 * @Date 2024/12/27 18:28
 **/
@Entity
@Table(name = "ppd_credit_apply_record")
public class PpdCreditApplyRecord  extends BaseEntity {
    /**
     * 授信请求流水号
     */
    private String loanReqNo;

    /**
     * 授信申请时间，格式 yyyyMMddHHmmss
     */
    private String creditApplyTime;

    /**
     * 请求方代码
     */
    private String sourceCode;

    /**
     * 客户名称
     */
    private String custName;

    /**
     * 证件类型，1: 身份证（暂时只支持该选项）
     */
    private String idType;

    /**
     * 证号证件
     */
    private String idNo;

    /**
     * 性别，1: 男，2: 女
     */
    private String sex;

    /**
     * 年龄，按身份证计算
     */
    private Integer age;

    /**
     * 手机号
     */
    private String mobileNo;

    /**
     * 婚姻状况，1: 未婚，2: 已婚，3: 其他
     */
    private String marriage;

    /**
     * 学历等级
     */
    private String eduLevel;

    /**
     * 最高学位
     */
    private String highestDegree;

    /**
     * 现居地址
     */
    private String address;

    /**
     * 职业
     */
    private String occupation;

    /**
     * 借款金额，单位: 元
     */
    private BigDecimal loanAmt;

    /**
     * 期数
     */
    private Integer loanTerm;

    /**
     * 借款用途
     */
    private String loanPurpose;

    /**
     * 期限类型
     */
    private String periodType;

    /**
     * 还款方式
     */
    private String refundMethod;

    /**
     * 扣款日类型
     */
    private String chargeDateType;

    /**
     * 贷款类型
     */
    private String loanType;

    /**
     * 放款银行代码
     */
    private String bankCode;

    /**
     * 放款卡号
     */
    private String bankAcct;

    /**
     * 放款银行卡账户名
     */
    private String acctName;

    /**
     * 放款卡持卡人预留手机号
     */
    private String bankMobile;

    /**
     * 还款银行代码
     */
    private String repayBankCode;

    /**
     * 还款卡号
     */
    private String repayBankAcct;

    /**
     * 还款银行卡账户名
     */
    private String repayAcctName;

    /**
     * 还款卡持卡人预留手机号
     */
    private String repayBankMobile;

    /**
     * 户籍地址（身份证 OCR）
     */
    private String addressOcr;

    /**
     * 身份证有效期起始日期
     */
    private String idBeginDate;

    /**
     * 身份证有效期结束日期，长期有效传 9999-12-31
     */
    private String idExpiryDate;

    /**
     * 签发机关
     */
    private String certificationUnit;

    /**
     * 民族
     */
    private String nation;

    /**
     * 国籍
     */
    private String nationality;

    /**
     * 联系人列表
     */
    private String contactList;

    /**
     * 渠道标识
     */
    private String accessType;

    private String faceRecoScore;

    private String faceRecoChannel;

    public String getLoanReqNo() {
        return loanReqNo;
    }

    public void setLoanReqNo(String loanReqNo) {
        this.loanReqNo = loanReqNo;
    }

    public String getCreditApplyTime() {
        return creditApplyTime;
    }

    public void setCreditApplyTime(String creditApplyTime) {
        this.creditApplyTime = creditApplyTime;
    }

    public String getSourceCode() {
        return sourceCode;
    }

    public void setSourceCode(String sourceCode) {
        this.sourceCode = sourceCode;
    }

    public String getCustName() {
        return custName;
    }

    public void setCustName(String custName) {
        this.custName = custName;
    }

    public String getIdType() {
        return idType;
    }

    public void setIdType(String idType) {
        this.idType = idType;
    }

    public String getIdNo() {
        return idNo;
    }

    public void setIdNo(String idNo) {
        this.idNo = idNo;
    }

    public String getSex() {
        return sex;
    }

    public void setSex(String sex) {
        this.sex = sex;
    }

    public Integer getAge() {
        return age;
    }

    public void setAge(Integer age) {
        this.age = age;
    }

    public String getMobileNo() {
        return mobileNo;
    }

    public void setMobileNo(String mobileNo) {
        this.mobileNo = mobileNo;
    }

    public String getMarriage() {
        return marriage;
    }

    public void setMarriage(String marriage) {
        this.marriage = marriage;
    }

    public String getEduLevel() {
        return eduLevel;
    }

    public void setEduLevel(String eduLevel) {
        this.eduLevel = eduLevel;
    }

    public String getHighestDegree() {
        return highestDegree;
    }

    public void setHighestDegree(String highestDegree) {
        this.highestDegree = highestDegree;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public String getOccupation() {
        return occupation;
    }

    public void setOccupation(String occupation) {
        this.occupation = occupation;
    }

    public BigDecimal getLoanAmt() {
        return loanAmt;
    }

    public void setLoanAmt(BigDecimal loanAmt) {
        this.loanAmt = loanAmt;
    }

    public Integer getLoanTerm() {
        return loanTerm;
    }

    public void setLoanTerm(Integer loanTerm) {
        this.loanTerm = loanTerm;
    }

    public String getLoanPurpose() {
        return loanPurpose;
    }

    public void setLoanPurpose(String loanPurpose) {
        this.loanPurpose = loanPurpose;
    }

    public String getPeriodType() {
        return periodType;
    }

    public void setPeriodType(String periodType) {
        this.periodType = periodType;
    }

    public String getRefundMethod() {
        return refundMethod;
    }

    public void setRefundMethod(String refundMethod) {
        this.refundMethod = refundMethod;
    }

    public String getChargeDateType() {
        return chargeDateType;
    }

    public void setChargeDateType(String chargeDateType) {
        this.chargeDateType = chargeDateType;
    }

    public String getLoanType() {
        return loanType;
    }

    public void setLoanType(String loanType) {
        this.loanType = loanType;
    }

    public String getBankCode() {
        return bankCode;
    }

    public void setBankCode(String bankCode) {
        this.bankCode = bankCode;
    }

    public String getBankAcct() {
        return bankAcct;
    }

    public void setBankAcct(String bankAcct) {
        this.bankAcct = bankAcct;
    }

    public String getAcctName() {
        return acctName;
    }

    public void setAcctName(String acctName) {
        this.acctName = acctName;
    }

    public String getBankMobile() {
        return bankMobile;
    }

    public void setBankMobile(String bankMobile) {
        this.bankMobile = bankMobile;
    }

    public String getRepayBankCode() {
        return repayBankCode;
    }

    public void setRepayBankCode(String repayBankCode) {
        this.repayBankCode = repayBankCode;
    }

    public String getRepayBankAcct() {
        return repayBankAcct;
    }

    public void setRepayBankAcct(String repayBankAcct) {
        this.repayBankAcct = repayBankAcct;
    }

    public String getRepayAcctName() {
        return repayAcctName;
    }

    public void setRepayAcctName(String repayAcctName) {
        this.repayAcctName = repayAcctName;
    }

    public String getRepayBankMobile() {
        return repayBankMobile;
    }

    public void setRepayBankMobile(String repayBankMobile) {
        this.repayBankMobile = repayBankMobile;
    }

    public String getAddressOcr() {
        return addressOcr;
    }

    public void setAddressOcr(String addressOcr) {
        this.addressOcr = addressOcr;
    }

    public String getIdBeginDate() {
        return idBeginDate;
    }

    public void setIdBeginDate(String idBeginDate) {
        this.idBeginDate = idBeginDate;
    }

    public String getIdExpiryDate() {
        return idExpiryDate;
    }

    public void setIdExpiryDate(String idExpiryDate) {
        this.idExpiryDate = idExpiryDate;
    }

    public String getCertificationUnit() {
        return certificationUnit;
    }

    public void setCertificationUnit(String certificationUnit) {
        this.certificationUnit = certificationUnit;
    }

    public String getNation() {
        return nation;
    }

    public void setNation(String nation) {
        this.nation = nation;
    }

    public String getNationality() {
        return nationality;
    }

    public void setNationality(String nationality) {
        this.nationality = nationality;
    }

    public String getContactList() {
        return contactList;
    }

    public void setContactList(String contactList) {
        this.contactList = contactList;
    }

    public String getFaceRecoScore() {
        return faceRecoScore;
    }

    public void setFaceRecoScore(String faceRecoScore) {
        this.faceRecoScore = faceRecoScore;
    }

    public String getFaceRecoChannel() {
        return faceRecoChannel;
    }

    public void setFaceRecoChannel(String faceRecoChannel) {
        this.faceRecoChannel = faceRecoChannel;
    }

    public String getAccessType() {
        return accessType;
    }

    public void setAccessType(String accessType) {
        this.accessType = accessType;
    }
}
