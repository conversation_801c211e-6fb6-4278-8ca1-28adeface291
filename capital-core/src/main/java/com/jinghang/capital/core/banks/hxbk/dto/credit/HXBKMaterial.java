package com.jinghang.capital.core.banks.hxbk.dto.credit;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * HXBK资料文件
 *
 * @Author: Lior
 * @CreateTime: 2025/7/8 10:20
 */
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class HXBKMaterial {

    /**
     * 资料类型
     * 授信阶段
     * 0-风控报告
     * 1-合同
     * 2-图片
     * 3-附件
     *
     * 用信阶段
     * 4-风控报告
     * 5-合同
     * 6-图片
     * 7-附件
     */
    @JsonProperty("m_type")
    private String mType;

    /**
     * 大类编码
     * 00-风控报告
     * 10-合同
     * 20-身份证图片
     * 26-人脸图片
     * 30-附件
     */
    @JsonProperty("big_code")
    private String bigCode;

    /**
     * 小类编码
     * 201-身份证人脸面
     * 202-身份证国徽面
     * 212-活体人脸图片
     */
    @JsonProperty("small_code")
    private String smallCode;

    /**
     * 资料名称
     */
    @JsonProperty("meterial_name")
    private String meterialName;

    /**
     * 文件地址
     */
    @JsonProperty("file_path")
    private String filePath;

    // Getter and Setter methods
    @JsonIgnore
    public String getMType() {
        return mType;
    }

    public void setMType(String mType) {
        this.mType = mType;
    }

    public String getBigCode() {
        return bigCode;
    }

    public void setBigCode(String bigCode) {
        this.bigCode = bigCode;
    }

    public String getSmallCode() {
        return smallCode;
    }

    public void setSmallCode(String smallCode) {
        this.smallCode = smallCode;
    }

    public String getMeterialName() {
        return meterialName;
    }

    public void setMeterialName(String meterialName) {
        this.meterialName = meterialName;
    }

    public String getFilePath() {
        return filePath;
    }

    public void setFilePath(String filePath) {
        this.filePath = filePath;
    }
}
